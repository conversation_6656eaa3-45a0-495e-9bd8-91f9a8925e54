# Задача 5: Уникальные
# Напишите функцию, которая возвращает список с уникальными элементами.

def unique(numbers):
    """Функция для получения списка с уникальными элементами"""
    unique_list = []
    for number in numbers:
        if number not in unique_list:
            unique_list.append(number)
    return unique_list

# Тестирование функции
print(unique([1, 2, 2, 3, 3, 3, 4]))       # Ожидаемый результат: [1, 2, 3, 4]
print(unique([5, 5, 5, 5]))                # Ожидаемый результат: [5]
print(unique([1, 2, 3, 4, 5]))             # Ожидаемый результат: [1, 2, 3, 4, 5]
print(unique([]))                          # Ожидаемый результат: []
print(unique([7, 3, 7, 1, 3, 9, 1]))       # Ожидаемый результат: [7, 3, 1, 9]

# Альтернативная реализация с использованием set()
def unique_set(numbers):
    """Альтернативная функция с использованием set()"""
    return list(set(numbers))

# Сравнение двух подходов
test_lists = [
    [1, 2, 2, 3, 3, 3, 4],
    [5, 1, 3, 1, 5, 9, 3],
    [10, 20, 10, 30, 20],
    []
]

print("\nСравнение двух подходов:")
for lst in test_lists:
    result1 = unique(lst)
    result2 = sorted(unique_set(lst))  # Сортируем для сравнения
    print(f"Исходный список: {lst}")
    print(f"Наша функция: {result1}")
    print(f"Через set(): {result2}")
    print(f"Порядок сохранен в нашей функции: {result1 == sorted(result1) or len(set(result1)) == len(result1)}")
    print()
