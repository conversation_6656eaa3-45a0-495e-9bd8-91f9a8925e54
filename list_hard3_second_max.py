# Сложная задача 3: Второй максимум
# Создайте список из случайных чисел. Найдите второй максимум.

from random import randint

# Создаем список из случайных чисел
random_numbers = []
for i in range(10):
    random_number = randint(1, 50)
    random_numbers.append(random_number)

print("Список случайных чисел:")
print(random_numbers)

# Способ 1: Используем сортировку
sorted_unique = sorted(set(random_numbers), reverse=True)
if len(sorted_unique) >= 2:
    second_max_1 = sorted_unique[1]
    print(f"Второй максимум (способ 1): {second_max_1}")
else:
    print("В списке недостаточно различных элементов для второго максимума")

# Способ 2: Поиск через два прохода
first_max = max(random_numbers)
second_max_2 = None

for number in random_numbers:
    if number < first_max:
        if second_max_2 is None or number > second_max_2:
            second_max_2 = number

if second_max_2 is not None:
    print(f"Второй максимум (способ 2): {second_max_2}")
else:
    print("Второй максимум не найден (все элементы одинаковые)")

# Способ 3: Поиск за один проход
first_max_3 = second_max_3 = float('-inf')

for number in random_numbers:
    if number > first_max_3:
        second_max_3 = first_max_3
        first_max_3 = number
    elif number > second_max_3 and number < first_max_3:
        second_max_3 = number

if second_max_3 != float('-inf'):
    print(f"Второй максимум (способ 3): {second_max_3}")
else:
    print("Второй максимум не найден")

print(f"Первый максимум: {first_max}")
print(f"Уникальные элементы (отсортированы): {sorted(set(random_numbers), reverse=True)}")
