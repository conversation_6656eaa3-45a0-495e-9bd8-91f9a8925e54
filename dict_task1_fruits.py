# Задача 1: Фрукты
# Пользователь вводит число K - количество фруктов. 
# Затем он вводит K фруктов в формате: название фрукта и его количество. 
# Добавьте все фрукты в словарь, где название фрукта - это ключ, а количество - значение.

# Ввод количества фруктов
k = int(input("Введите количество фруктов: "))

# Создаем пустой словарь для фруктов
fruits = {}

# Вводим данные о фруктах
for i in range(k):
    fruit_name = input(f"Введите название {i+1}-го фрукта: ")
    fruit_count = int(input(f"Введите количество {fruit_name}: "))
    fruits[fruit_name] = fruit_count

# Выводим результат
print("Словарь фруктов:")
print(fruits)

# Дополнительная информация
print(f"\nВсего видов фруктов: {len(fruits)}")
total_fruits = sum(fruits.values())
print(f"Общее количество фруктов: {total_fruits}")

# Показываем каждый фрукт отдельно
print("\nПодробная информация:")
for fruit, count in fruits.items():
    print(f"{fruit}: {count} шт.")
