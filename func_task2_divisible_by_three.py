# Задача 2: На три
# Напишите функцию, которая возвращает True, если число делится на 3, и False в противном случае.

def three(number):
    """Функция для проверки, делится ли число на 3"""
    return number % 3 == 0

# Тестирование функции
print(three(9))   # Ожидаемый результат: True
print(three(10))  # Ожидаемый результат: False
print(three(0))   # Ожидаемый результат: True
print(three(15))  # Ожидаемый результат: True
print(three(7))   # Ожидаемый результат: False

# Дополнительные тесты
test_numbers = [3, 6, 12, 13, 18, 21, 22, 27]
print("\nТестирование различных чисел:")
for num in test_numbers:
    result = three(num)
    print(f"{num} делится на 3: {result}")
