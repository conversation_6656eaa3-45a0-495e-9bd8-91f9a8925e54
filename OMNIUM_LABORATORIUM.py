# ================================================================
# OMNIUM LABORATORIUM PYTHONIS
# ВСЕ ЛАБОРАТОРНЫЕ РАБОТЫ ПО PYTHON
# ================================================================
# Главное меню для доступа ко всем лабораторным работам
# Menu principale pro omnibus laboratoriis

import subprocess
import sys
import os

def run_lab(filename):
    """Запуск лабораторной работы"""
    try:
        if os.path.exists(filename):
            subprocess.run([sys.executable, filename], check=True)
        else:
            print(f"Файл {filename} не найден!")
            input("Нажмите Enter для продолжения...")
    except subprocess.CalledProcessError as e:
        print(f"Ошибка при запуске {filename}: {e}")
        input("Нажмите Enter для продолжения...")
    except KeyboardInterrupt:
        print("\nВозврат в главное меню...")

def show_main_menu():
    """Показать главное меню"""
    print("\n" + "="*60)
    print("🐍 OMNIUM LABORATORIUM PYTHONIS 🐍")
    print("ВСЕ ЛАБОРАТОРНЫЕ РАБОТЫ ПО PYTHON")
    print("="*60)
    print("📚 Выберите лабораторную работу:")
    print()
    print("1. 🔢 Переменные и типы данных (INPUT_ET_OUTPUT_DATA.py)")
    print("2. 🔀 Операторы (OPERATORES_PYTHONIS.py)")
    print("3. 🔀 Условные операторы (CONDITIONALES_PYTHONIS.py)")
    print("4. 🔄 Цикл for (CYCLUS_FOR_PYTHONIS.py)")
    print("5. 🔁 Цикл while (CYCLUS_WHILE_PYTHONIS.py)")
    print("6. 📋 Списки (LISTA_PYTHONIS.py)")
    print("7. 🔧 Функции (FUNCTIONES_PYTHONIS.py)")
    print("8. 📝 Строки (STRINGAE_PYTHONIS.py)")
    print("9. 📚 Словари (DICTIONARIES_PYTHONIS.py)")
    print()
    print("0. 🚪 Выход")
    print("="*60)

def show_lab_info():
    """Показать информацию о лабораторных работах"""
    print("\n" + "="*60)
    print("📖 ИНФОРМАЦИЯ О ЛАБОРАТОРНЫХ РАБОТАХ")
    print("="*60)
    
    labs_info = {
        "1": {
            "name": "Переменные и типы данных",
            "file": "INPUT_ET_OUTPUT_DATA.py",
            "tasks": [
                "Ввод и вывод данных",
                "Работа с числами",
                "Арифметические операции",
                "Преобразование типов",
                "Форматирование строк"
            ]
        },
        "2": {
            "name": "Операторы",
            "file": "OPERATORES_PYTHONIS.py", 
            "tasks": [
                "Арифметические операторы",
                "Операторы сравнения",
                "Логические операторы",
                "Битовые операторы",
                "Практические задачи"
            ]
        },
        "3": {
            "name": "Условные операторы",
            "file": "CONDITIONALES_PYTHONIS.py", 
            "tasks": [
                "Простые условия",
                "Сложные условия",
                "Вложенные условия",
                "Логические операторы",
                "Практические задачи"
            ]
        },
        "4": {
            "name": "Цикл for",
            "file": "CYCLUS_FOR_PYTHONIS.py",
            "tasks": [
                "Простые циклы",
                "Циклы с range()",
                "Вложенные циклы",
                "Работа с последовательностями",
                "Практические применения"
            ]
        },
        "5": {
            "name": "Цикл while",
            "file": "CYCLUS_WHILE_PYTHONIS.py",
            "tasks": [
                "Базовые циклы while",
                "Условия выхода",
                "Накопление значений",
                "Поиск экстремумов",
                "Алгоритмы (факториал, Фибоначчи)"
            ]
        },
        "6": {
            "name": "Списки",
            "file": "LISTA_PYTHONIS.py",
            "tasks": [
                "Создание и изменение списков",
                "Методы списков",
                "Срезы и индексы",
                "Поиск и сортировка",
                "Сложные алгоритмы"
            ]
        },
        "7": {
            "name": "Функции",
            "file": "FUNCTIONES_PYTHONIS.py",
            "tasks": [
                "Определение функций",
                "Параметры и возврат значений",
                "Область видимости",
                "Практические функции",
                "Модульность кода"
            ]
        },
        "8": {
            "name": "Строки",
            "file": "STRINGAE_PYTHONIS.py",
            "tasks": [
                "Основы работы со строками",
                "Методы строк",
                "Поиск и замена",
                "Форматирование",
                "Алгоритмы обработки текста"
            ]
        },
        "9": {
            "name": "Словари",
            "file": "DICTIONARIES_PYTHONIS.py",
            "tasks": [
                "Создание словарей",
                "Методы словарей",
                "Итерация по словарям",
                "Вложенные структуры данных",
                "Практические применения"
            ]
        }
    }
    
    for num, info in labs_info.items():
        print(f"\n{num}. {info['name']} ({info['file']})")
        print("   Темы:")
        for task in info['tasks']:
            print(f"   • {task}")
    
    input("\nНажмите Enter для возврата в меню...")

def main():
    """Главная функция программы"""
    lab_files = {
        1: "INPUT_ET_OUTPUT_DATA.py",
        2: "OPERATORES_PYTHONIS.py",
        3: "CONDITIONALES_PYTHONIS.py", 
        4: "CYCLUS_FOR_PYTHONIS.py",
        5: "CYCLUS_WHILE_PYTHONIS.py",
        6: "LISTA_PYTHONIS.py",
        7: "FUNCTIONES_PYTHONIS.py",
        8: "STRINGAE_PYTHONIS.py",
        9: "DICTIONARIES_PYTHONIS.py"
    }
    
    while True:
        show_main_menu()
        try:
            choice = input("Введите номер лабораторной работы (0 для выхода, ? для справки): ").strip()
            
            if choice == "0":
                print("\n🎓 Спасибо за изучение Python!")
                print("Gratias tibi ago pro studio Pythonis!")
                break
            elif choice == "?" or choice.lower() == "help":
                show_lab_info()
            elif choice.isdigit() and int(choice) in lab_files:
                lab_num = int(choice)
                print(f"\n🚀 Запуск лабораторной работы {lab_num}...")
                run_lab(lab_files[lab_num])
            else:
                print("❌ Неверный выбор! Введите номер от 1 до 9, 0 для выхода или ? для справки.")
                input("Нажмите Enter для продолжения...")
                
        except KeyboardInterrupt:
            print("\n\n👋 Программа завершена пользователем.")
            break
        except Exception as e:
            print(f"❌ Произошла ошибка: {e}")
            input("Нажмите Enter для продолжения...")

if __name__ == "__main__":
    print("🐍 Добро пожаловать в мир Python!")
    print("Salve in mundum Pythonis!")
    main()
