# Задача 10: Среднее арифметическое
# Найдите среднее арифметическое элементов списка из задания 7

from random import randint

# Создаем список из случайных чисел (как в задании 7)
random_numbers = []
for i in range(10):
    random_number = randint(1, 100)
    random_numbers.append(random_number)

print("Список случайных чисел:")
print(random_numbers)

# Находим среднее арифметическое
total_sum = sum(random_numbers)
count = len(random_numbers)
average = total_sum / count

print(f"Сумма элементов: {total_sum}")
print(f"Количество элементов: {count}")
print(f"Среднее арифметическое: {average}")

# Альтернативный способ:
average_manual = sum(random_numbers) / len(random_numbers)
print(f"Среднее арифметическое (альтернативный расчет): {average_manual}")
