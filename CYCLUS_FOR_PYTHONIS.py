# ================================================================
# ЛАБОРАТОРНАЯ РАБОТА: ЦИКЛ FOR
# ================================================================

def task1_numbers_0_to_n():
    """Задача 1: Вывод чисел от 0 до N"""
    print("\n=== Задача 1: Вывод чисел от 0 до N ===")
    N = int(input('Введите число N: '))
    print(f"Числа от 0 до {N}:")
    for i in range(N + 1):
        print(i)

def task2_numbers_k_to_n():
    """Задача 2: Вывод чисел от K до N"""
    print("\n=== Задача 2: Вывод чисел от K до N ===")
    K = int(input('Введите число K: '))
    N = int(input('Введите число N: '))
    print(f"Числа от {K} до {N}:")
    for i in range(K, N + 1):
        print(i)

def task3_sum_k_to_n():
    """Задача 3: Сумма от K до N"""
    print("\n=== Задача 3: Сумма от K до N ===")
    K = int(input('Введите число K: '))
    N = int(input('Введите число N: '))
    total_sum = 0
    for i in range(K, N + 1):
        total_sum += i
    print(f"Сумма чисел от {K} до {N}: {total_sum}")

def task4_sum_even_k_to_n():
    """Задача 4: Сумма четных от K до N"""
    print("\n=== Задача 4: Сумма четных от K до N ===")
    K = int(input('Введите число K: '))
    N = int(input('Введите число N: '))
    even_sum = 0
    for i in range(K, N + 1):
        if i % 2 == 0:
            even_sum += i
    print(f"Сумма четных чисел от {K} до {N}: {even_sum}")

def task5_sum_fractions_1():
    """Задача 5: Сумма дробей (часть первая)"""
    print("\n=== Задача 5: Сумма дробей (часть первая) ===")
    print("Сумма: 1 + 1.1 + 1.2 + ... + (1 + N/10)")
    N = int(input('Введите число N: '))
    total_sum = 0
    for i in range(N + 1):
        term = 1 + i / 10
        total_sum += term
    print(f"Результат: {total_sum}")

def task6_sum_fractions_2():
    """Задача 6: Сумма дробей (часть вторая)"""
    print("\n=== Задача 6: Сумма дробей (часть вторая) ===")
    print("Сумма: 1 + 1/2 + 1/3 + ... + 1/N")
    N = int(input('Введите число N: '))
    total_sum = 0
    for i in range(1, N + 1):
        term = 1 / i
        total_sum += term
    print(f"Результат: {round(total_sum, 3)}")

def show_menu():
    """Показать меню выбора задач"""
    print("\n" + "="*50)
    print("ЛАБОРАТОРНАЯ РАБОТА: ЦИКЛ FOR В PYTHON")
    print("CYCLUS FOR PYTHONIS - LABORATORIUM OPUS")
    print("="*50)
    print("Выберите задачу:")
    print("1. Вывод чисел от 0 до N")
    print("2. Вывод чисел от K до N")
    print("3. Сумма от K до N")
    print("4. Сумма четных от K до N")
    print("5. Сумма дробей (часть первая)")
    print("6. Сумма дробей (часть вторая)")
    print("0. Выход")
    print("="*50)

def main():
    """Основная функция программы"""
    tasks = {
        1: task1_numbers_0_to_n,
        2: task2_numbers_k_to_n,
        3: task3_sum_k_to_n,
        4: task4_sum_even_k_to_n,
        5: task5_sum_fractions_1,
        6: task6_sum_fractions_2
    }
    
    while True:
        show_menu()
        try:
            choice = int(input("Введите номер задачи (0 для выхода): "))
            
            if choice == 0:
                print("Вы вышли из программы)")
                break
            elif choice in tasks:
                tasks[choice]()
                input("\nНажмите Enter для продолжения...")
            else:
                print("Неверный выбор! Пожалуйста, выберите номер от 0 до 6.")
        except ValueError:
            print("Ошибка! Введите число от 0 до 6.")
        except KeyboardInterrupt:
            print("\n\nПрограмма завершена пользователем.")
            break

if __name__ == "__main__":
    main()
