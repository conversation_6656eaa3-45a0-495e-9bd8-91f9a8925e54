# Сложная задача 4: Различные элементы
# Создайте список из случайных чисел. Найдите количество различных элементов в нем.

from random import randint

# Создаем список из случайных чисел
random_numbers = []
for i in range(15):
    random_number = randint(1, 10)  # Используем меньший диапазон для повторений
    random_numbers.append(random_number)

print("Список случайных чисел:")
print(random_numbers)

# Способ 1: Используем set() для получения уникальных элементов
unique_elements = set(random_numbers)
unique_count_1 = len(unique_elements)

print(f"Уникальные элементы: {sorted(unique_elements)}")
print(f"Количество различных элементов (способ 1): {unique_count_1}")

# Способ 2: Подсчет вручную
unique_elements_manual = []
for number in random_numbers:
    if number not in unique_elements_manual:
        unique_elements_manual.append(number)

unique_count_2 = len(unique_elements_manual)
print(f"Количество различных элементов (способ 2): {unique_count_2}")

# Способ 3: Используем словарь для подсчета
element_counts = {}
for number in random_numbers:
    element_counts[number] = element_counts.get(number, 0) + 1

unique_count_3 = len(element_counts)
print(f"Количество различных элементов (способ 3): {unique_count_3}")

# Показываем статистику
print("\nСтатистика элементов:")
for element in sorted(element_counts.keys()):
    count = element_counts[element]
    print(f"Элемент {element}: встречается {count} раз")

print(f"\nОбщее количество элементов: {len(random_numbers)}")
print(f"Количество различных элементов: {unique_count_1}")
