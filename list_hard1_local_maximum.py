# Сложная задача 1: Локальный максимум
# Создайте список из случайных чисел. Найдите номер его последнего локального максимума
# (локальный максимум — это элемент, который больше любого из своих соседей).

from random import randint

# Создаем список из случайных чисел
random_numbers = []
for i in range(10):
    random_number = randint(1, 20)  # Используем меньший диапазон для наглядности
    random_numbers.append(random_number)

print("Список случайных чисел:")
print(random_numbers)

# Ищем последний локальный максимум
last_local_max_index = -1

# Проверяем элементы, исключая первый и последний (у них нет двух соседей)
for i in range(1, len(random_numbers) - 1):
    # Локальный максимум: элемент больше левого И правого соседа
    if random_numbers[i] > random_numbers[i-1] and random_numbers[i] > random_numbers[i+1]:
        last_local_max_index = i

if last_local_max_index != -1:
    print(f"Номер последнего локального максимума: {last_local_max_index}")
    print(f"Значение: {random_numbers[last_local_max_index]}")
else:
    print("Локальных максимумов не найдено")

# Показываем все локальные максимумы для наглядности
print("\nВсе локальные максимумы:")
for i in range(1, len(random_numbers) - 1):
    if random_numbers[i] > random_numbers[i-1] and random_numbers[i] > random_numbers[i+1]:
        print(f"Индекс {i}: значение {random_numbers[i]}")
