# ================================================================
# ЛАБОРАТОРНАЯ РАБОТА: СТРОКИ В PYTHON
# LABORATORIUM OPUS: STRINGAE PYTHONIS
# ================================================================
# Все задачи в одном файле
# Omnes exercitationes in uno archivo

def task1_length():
    """Задача 1: Длина строки"""
    print("\n=== Задача 1: Длина строки ===")
    user_string = input("Введите строку: ")
    length = len(user_string)
    print(f"Длина строки '{user_string}': {length}")

def task2_first_last():
    """Задача 2: Первый и последний символ"""
    print("\n=== Задача 2: Первый и последний символ ===")
    user_string = input("Введите строку: ")
    if len(user_string) > 0:
        print(f"Первый символ: '{user_string[0]}'")
        print(f"Последний символ: '{user_string[-1]}'")
    else:
        print("Строка пустая!")

def task3_reverse():
    """Задача 3: Обращение строки"""
    print("\n=== Задача 3: Обращение строки ===")
    user_string = input("Введите строку: ")
    reversed_string = user_string[::-1]
    print(f"Исходная: '{user_string}'")
    print(f"Обращенная: '{reversed_string}'")

def task4_vowels():
    """Задача 4: Подсчет гласных"""
    print("\n=== Задача 4: Подсчет гласных ===")
    user_string = input("Введите строку: ")
    vowels = "aeiouаеёиоуыэюя"
    count = sum(1 for char in user_string.lower() if char in vowels)
    found = [char for char in user_string.lower() if char in vowels]
    print(f"Количество гласных: {count}")
    print(f"Найденные гласные: {found}")

def task5_palindrome():
    """Задача 5: Палиндром"""
    print("\n=== Задача 5: Палиндром ===")
    user_string = input("Введите строку: ")
    cleaned = user_string.lower().replace(" ", "")
    is_palindrome = cleaned == cleaned[::-1]
    print(f"Строка '{user_string}' {'является' if is_palindrome else 'не является'} палиндромом")

def task6_word_count():
    """Задача 6: Количество слов"""
    print("\n=== Задача 6: Количество слов ===")
    user_string = input("Введите строку: ")
    words = user_string.split()
    count = len(words)
    print(f"Количество слов: {count}")
    print(f"Слова: {words}")

def task7_replace_spaces():
    """Задача 7: Замена символов"""
    print("\n=== Задача 7: Замена пробелов на подчеркивания ===")
    user_string = input("Введите строку: ")
    replaced = user_string.replace(" ", "_")
    print(f"Исходная: '{user_string}'")
    print(f"Замененная: '{replaced}'")

def task8_find_substring():
    """Задача 8: Поиск подстроки"""
    print("\n=== Задача 8: Поиск подстроки ===")
    user_string = input("Введите строку: ")
    substring = input("Введите подстроку для поиска: ")
    position = user_string.find(substring)
    if position != -1:
        print(f"Подстрока найдена на позиции: {position}")
    else:
        print("Подстрока не найдена")

def task9_remove_spaces():
    """Задача 9: Удаление пробелов"""
    print("\n=== Задача 9: Удаление пробелов ===")
    user_string = input("Введите строку: ")
    no_spaces = user_string.replace(" ", "")
    removed_count = len(user_string) - len(no_spaces)
    print(f"Исходная: '{user_string}'")
    print(f"Без пробелов: '{no_spaces}'")
    print(f"Удалено пробелов: {removed_count}")

def task10_capitalize():
    """Задача 10: Капитализация"""
    print("\n=== Задача 10: Капитализация ===")
    user_string = input("Введите строку: ")
    capitalized = user_string.title()
    print(f"Исходная: '{user_string}'")
    print(f"Капитализированная: '{capitalized}'")

def show_menu():
    """Показать меню выбора задач"""
    print("\n" + "="*50)
    print("ЛАБОРАТОРНАЯ РАБОТА: СТРОКИ В PYTHON")
    print("STRINGAE PYTHONIS - LABORATORIUM OPUS")
    print("="*50)
    print("Выберите задачу:")
    print("1. Длина строки")
    print("2. Первый и последний символ")
    print("3. Обращение строки")
    print("4. Подсчет гласных")
    print("5. Палиндром")
    print("6. Количество слов")
    print("7. Замена символов")
    print("8. Поиск подстроки")
    print("9. Удаление пробелов")
    print("10. Капитализация")
    print("0. Выход")
    print("="*50)

def main():
    """Основная функция программы"""
    tasks = {
        1: task1_length, 2: task2_first_last, 3: task3_reverse,
        4: task4_vowels, 5: task5_palindrome, 6: task6_word_count,
        7: task7_replace_spaces, 8: task8_find_substring,
        9: task9_remove_spaces, 10: task10_capitalize
    }
    
    while True:
        show_menu()
        try:
            choice = int(input("Введите номер задачи (0 для выхода): "))
            if choice == 0:
                print("Спасибо за использование программы!")
                break
            elif choice in tasks:
                tasks[choice]()
                input("\nНажмите Enter для продолжения...")
            else:
                print("Неверный выбор! Выберите номер от 0 до 10.")
        except ValueError:
            print("Ошибка! Введите число от 0 до 10.")
        except KeyboardInterrupt:
            print("\n\nПрограмма завершена пользователем.")
            break

if __name__ == "__main__":
    main()
