# Задача 7: Наибольший элемент
# Создайте список из случайных чисел и найдите наибольший элемент в нем.

from random import randint

# Создаем список из случайных чисел
random_numbers = []
for i in range(10):
    random_number = randint(1, 100)
    random_numbers.append(random_number)

print("Список случайных чисел:")
print(random_numbers)

# Находим наибольший элемент
max_element = max(random_numbers)

print(f"Наибольший элемент: {max_element}")

# Альтернативный способ через цикл:
max_manual = random_numbers[0]
for number in random_numbers:
    if number > max_manual:
        max_manual = number

print(f"Наибольший элемент (найден вручную): {max_manual}")
