# ================================================================
# TEST OMNIUM LABORATORIUM PYTHONIS
# ТЕСТИРОВАНИЕ ВСЕХ ЛАБОРАТОРНЫХ РАБОТ ПО PYTHON
# ================================================================
# Автоматические тесты для проверки корректности решений
# Testes automatici pro verificandis solutionibus

import os
import sys
import glob

def test_file_exists(filename):
    """Проверить существование файла"""
    if os.path.exists(filename):
        print(f"✅ {filename} - файл найден")
        return True
    else:
        print(f"❌ {filename} - файл не найден")
        return False

def test_python_syntax(filename):
    """Проверить синтаксис Python файла"""
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            code = f.read()
        compile(code, filename, 'exec')
        print(f"✅ {filename} - синтаксис корректен")
        return True
    except SyntaxError as e:
        print(f"❌ {filename} - ошибка синтаксиса: {e}")
        return False
    except Exception as e:
        print(f"⚠️ {filename} - предупреждение: {e}")
        return True

def test_lab_files():
    """Тестировать все файлы лабораторных работ"""
    print("🧪 ТЕСТИРОВАНИЕ ФАЙЛОВ ЛАБОРАТОРНЫХ РАБОТ")
    print("="*50)
    
    lab_files = [
        "OMNIUM_LABORATORIUM.py",
        "INPUT_ET_OUTPUT_DATA.py", 
        "OPERATORES_PYTHONIS.py",
        "CONDITIONALES_PYTHONIS.py",
        "CYCLUS_FOR_PYTHONIS.py",
        "CYCLUS_WHILE_PYTHONIS.py",
        "LISTA_PYTHONIS.py",
        "FUNCTIONES_PYTHONIS.py",
        "STRINGAE_PYTHONIS.py",
        "DICTIONARIES_PYTHONIS.py"
    ]
    
    passed = 0
    total = len(lab_files)
    
    for filename in lab_files:
        print(f"\n📁 Тестирование {filename}:")
        exists = test_file_exists(filename)
        if exists:
            syntax_ok = test_python_syntax(filename)
            if syntax_ok:
                passed += 1
    
    print(f"\n📊 РЕЗУЛЬТАТЫ ТЕСТИРОВАНИЯ:")
    print(f"Пройдено: {passed}/{total}")
    print(f"Процент успеха: {(passed/total)*100:.1f}%")
    
    return passed == total

def test_individual_tasks():
    """Тестировать отдельные файлы задач"""
    print("\n🔍 ТЕСТИРОВАНИЕ ОТДЕЛЬНЫХ ЗАДАЧ")
    print("="*50)
    
    task_patterns = [
        "task*.py",
        "calc_task*.py", 
        "ifelse_task*.py",
        "for_task*.py",
        "while_task*.py",
        "list_task*.py",
        "func_task*.py",
        "string_task*.py",
        "dict_task*.py"
    ]
    
    all_tasks = []
    
    for pattern in task_patterns:
        tasks = glob.glob(pattern)
        all_tasks.extend(tasks)
    
    if not all_tasks:
        print("ℹ️ Отдельные файлы задач не найдены")
        return True
    
    passed = 0
    total = len(all_tasks)
    
    for task_file in sorted(all_tasks):
        exists = test_file_exists(task_file)
        if exists:
            syntax_ok = test_python_syntax(task_file)
            if syntax_ok:
                passed += 1
    
    print(f"\n📊 РЕЗУЛЬТАТЫ ТЕСТИРОВАНИЯ ЗАДАЧ:")
    print(f"Пройдено: {passed}/{total}")
    print(f"Процент успеха: {(passed/total)*100:.1f}%")
    
    return passed == total

def test_readme():
    """Проверить README файл"""
    print("\n📖 ТЕСТИРОВАНИЕ README")
    print("="*30)
    
    if test_file_exists("README.md"):
        try:
            with open("README.md", 'r', encoding='utf-8') as f:
                content = f.read()
            
            required_sections = [
                "Лабораторные работы по Python",
                "Быстрый запуск",
                "OMNIUM_LABORATORIUM.py",
                "DICTIONARIES_PYTHONIS.py"
            ]
            
            missing_sections = []
            for section in required_sections:
                if section not in content:
                    missing_sections.append(section)
            
            if not missing_sections:
                print("✅ README.md содержит все необходимые разделы")
                return True
            else:
                print(f"⚠️ README.md - отсутствуют разделы: {missing_sections}")
                return False
                
        except Exception as e:
            print(f"❌ Ошибка чтения README.md: {e}")
            return False
    else:
        return False

def show_statistics():
    """Показать статистику проекта"""
    print("\n📈 СТАТИСТИКА ПРОЕКТА")
    print("="*30)
    
    # Подсчет файлов
    py_files = [f for f in os.listdir('.') if f.endswith('.py')]
    md_files = [f for f in os.listdir('.') if f.endswith('.md')]
    
    print(f"Python файлов: {len(py_files)}")
    print(f"Markdown файлов: {len(md_files)}")
    
    # Подсчет строк кода
    total_lines = 0
    for py_file in py_files:
        try:
            with open(py_file, 'r', encoding='utf-8') as f:
                lines = len(f.readlines())
                total_lines += lines
                print(f"  {py_file}: {lines} строк")
        except:
            pass
    
    print(f"\nОбщее количество строк кода: {total_lines}")
    
    # Подсчет задач
    task_files = [f for f in py_files if 'task' in f]
    print(f"Файлов с отдельными задачами: {len(task_files)}")

def main():
    """Главная функция тестирования"""
    print("🐍 ТЕСТИРОВАНИЕ ЛАБОРАТОРНЫХ РАБОТ ПО PYTHON")
    print("TEST OMNIUM LABORATORIUM PYTHONIS")
    print("="*60)
    
    all_tests_passed = True
    
    # Тестирование основных файлов
    lab_tests = test_lab_files()
    all_tests_passed = all_tests_passed and lab_tests
    
    # Тестирование отдельных задач
    task_tests = test_individual_tasks()
    all_tests_passed = all_tests_passed and task_tests
    
    # Тестирование README
    readme_tests = test_readme()
    all_tests_passed = all_tests_passed and readme_tests
    
    # Статистика
    show_statistics()
    
    # Итоговый результат
    print("\n" + "="*60)
    if all_tests_passed:
        print("🎉 ВСЕ ТЕСТЫ ПРОЙДЕНЫ УСПЕШНО!")
        print("Omnes testes feliciter transierunt!")
        print("📚 Все 9 лабораторных работ готовы к использованию!")
    else:
        print("⚠️ НЕКОТОРЫЕ ТЕСТЫ НЕ ПРОЙДЕНЫ")
        print("Aliqui testes non transierunt")
    print("="*60)
    
    return all_tests_passed

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n🛑 Тестирование прервано пользователем")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Критическая ошибка: {e}")
        sys.exit(1)
