# Задача 3: Максимум в списке
# Напишите функцию, которая возвращает максимальный элемент списка.

def maxList(numbers):
    """Функция для поиска максимального элемента в списке"""
    if not numbers:  # Проверка на пустой список
        return None
    
    max_element = numbers[0]
    for number in numbers:
        if number > max_element:
            max_element = number
    return max_element

# Тестирование функции
print(maxList([1, 2, 3, 4, 5]))      # Ожидаемый результат: 5
print(maxList([10, 5, 8, 3, 9]))     # Ожидаемый результат: 10
print(maxList([-1, -5, -3, -2]))     # Ожидаемый результат: -1
print(maxList([42]))                 # Ожидаемый результат: 42
print(maxList([]))                   # Ожидаемый результат: None

# Сравнение с встроенной функцией max()
test_lists = [
    [1, 2, 3, 4, 5],
    [10, 5, 8, 3, 9],
    [-1, -5, -3, -2],
    [100, 200, 50, 75]
]

print("\nСравнение с встроенной функцией max():")
for lst in test_lists:
    our_result = maxList(lst)
    builtin_result = max(lst)
    print(f"Список: {lst}")
    print(f"Наша функция: {our_result}, встроенная max(): {builtin_result}")
    print(f"Результаты совпадают: {our_result == builtin_result}")
    print()
