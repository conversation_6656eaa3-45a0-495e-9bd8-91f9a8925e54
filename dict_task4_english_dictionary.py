# Задача 4: Английский словарь
# Программа получает на вход слово на английском языке и несколько его переводов на русском языке. 
# Составьте словарь, в котором ключ - это английское слово, а значение - это список русских слов.

# Ввод количества слов
n = int(input("Введите количество слов: "))

# Создаем пустой словарь
english_dict = {}

# Вводим данные о словах
for i in range(n):
    print(f"\nСлово {i+1}:")
    line = input("Введите строку в формате 'english_word - перевод1, перевод2, ...': ")
    
    # Разделяем английское слово и переводы
    parts = line.split(' - ')
    english_word = parts[0].strip()
    translations_str = parts[1].strip()
    
    # Разделяем переводы по запятым и убираем лишние пробелы
    translations = [translation.strip() for translation in translations_str.split(',')]
    
    # Добавляем в словарь
    english_dict[english_word] = translations

# Выводим результат
print("\nАнглийский словарь:")
print(english_dict)

# Красивый вывод словаря
print("\nСловарь в удобном формате:")
for english, russian_list in english_dict.items():
    print(f"{english}: {', '.join(russian_list)}")

# Дополнительная информация
print(f"\nСтатистика словаря:")
print(f"Всего английских слов: {len(english_dict)}")

total_translations = sum(len(translations) for translations in english_dict.values())
print(f"Всего переводов: {total_translations}")

# Находим слово с наибольшим количеством переводов
max_translations_word = max(english_dict.items(), key=lambda x: len(x[1]))
print(f"Слово с наибольшим количеством переводов: {max_translations_word[0]} ({len(max_translations_word[1])} переводов)")

# Показываем все переводы для каждого слова
print(f"\nПодробная информация:")
for english, russian_list in english_dict.items():
    print(f"'{english}' имеет {len(russian_list)} перевод(ов): {russian_list}")
