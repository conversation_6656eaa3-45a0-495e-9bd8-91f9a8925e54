# Задача 2: Стар<PERSON>ий и младший
# Пользователь вводит число N. Затем он вводит личные данные (имя и возраст) своих N друзей. 
# Создайте список friends и добавьте в него N словарей с ключами name и age. 
# Найдите самого младшего из друзей и выведите его имя.

# Ввод количества друзей
n = int(input("Введите количество друзей: "))

# Создаем пустой список для друзей
friends = []

# Вводим данные о друзьях
for i in range(n):
    print(f"\nДруг {i+1}:")
    name = input("Введите имя: ")
    age = int(input("Введите возраст: "))
    
    # Создаем словарь для друга и добавляем в список
    friend = {"name": name, "age": age}
    friends.append(friend)

# Выводим список всех друзей
print("\nСписок всех друзей:")
for friend in friends:
    print(f"{friend['name']} - {friend['age']} лет")

# Находим самого младшего друга
youngest_friend = friends[0]  # Начинаем с первого друга
for friend in friends:
    if friend['age'] < youngest_friend['age']:
        youngest_friend = friend

# Выводим результат
print(f"\nСамый младший друг: {youngest_friend['name']}")
print(f"Возраст: {youngest_friend['age']} лет")

# Альтернативный способ с использованием функции min()
youngest_alt = min(friends, key=lambda x: x['age'])
print(f"\nПроверка (альтернативный способ): {youngest_alt['name']}")

# Дополнительная статистика
ages = [friend['age'] for friend in friends]
print(f"\nСтатистика возрастов:")
print(f"Минимальный возраст: {min(ages)}")
print(f"Максимальный возраст: {max(ages)}")
print(f"Средний возраст: {sum(ages) / len(ages):.1f}")
