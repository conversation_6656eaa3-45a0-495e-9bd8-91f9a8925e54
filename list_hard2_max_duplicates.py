# Сложная задача 2: Одинаковые элементы
# Создайте список из случайных чисел. Найдите максимальное количество его одинаковых элементов.

from random import randint

# Создаем список из случайных чисел
random_numbers = []
for i in range(15):
    random_number = randint(1, 10)  # Используем меньший диапазон для большей вероятности повторений
    random_numbers.append(random_number)

print("Список случайных чисел:")
print(random_numbers)

# Подсчитываем количество каждого элемента
element_counts = {}
for number in random_numbers:
    if number in element_counts:
        element_counts[number] += 1
    else:
        element_counts[number] = 1

print("Количество каждого элемента:")
for element, count in element_counts.items():
    print(f"Элемент {element}: {count} раз")

# Находим максимальное количество одинаковых элементов
max_count = max(element_counts.values())
most_frequent_elements = []

for element, count in element_counts.items():
    if count == max_count:
        most_frequent_elements.append(element)

print(f"\nМаксимальное количество одинаковых элементов: {max_count}")
print(f"Элементы с максимальным количеством повторений: {most_frequent_elements}")

# Альтернативный способ с использованием метода count()
max_count_alt = 0
for number in set(random_numbers):  # set() убирает дубликаты
    count = random_numbers.count(number)
    if count > max_count_alt:
        max_count_alt = count

print(f"Максимальное количество (альтернативный способ): {max_count_alt}")
