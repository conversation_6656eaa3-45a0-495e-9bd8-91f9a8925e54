# Задача 4: Сколько четных
# Напишите функцию, которая возвращает количество четных элементов в списке.

def evenCounter(numbers):
    """Функция для подсчета количества четных элементов в списке"""
    count = 0
    for number in numbers:
        if number % 2 == 0:
            count += 1
    return count

# Тестирование функции
print(evenCounter([1, 2, 3, 4, 5]))        # Ожидаемый результат: 2 (числа 2 и 4)
print(evenCounter([2, 4, 6, 8]))           # Ожидаемый результат: 4 (все четные)
print(evenCounter([1, 3, 5, 7]))           # Ожидаемый результат: 0 (все нечетные)
print(evenCounter([]))                     # Ожидаемый результат: 0 (пустой список)
print(evenCounter([0, 10, 15, 20, 25]))    # Ожидаемый результат: 3 (числа 0, 10, 20)

# Дополнительные тесты
test_lists = [
    [1, 2, 3, 4, 5, 6, 7, 8, 9, 10],
    [-2, -1, 0, 1, 2],
    [11, 13, 15, 17, 19],
    [2, 4, 6, 8, 10, 12]
]

print("\nДополнительное тестирование:")
for lst in test_lists:
    even_count = evenCounter(lst)
    even_numbers = [num for num in lst if num % 2 == 0]
    print(f"Список: {lst}")
    print(f"Количество четных: {even_count}")
    print(f"Четные числа: {even_numbers}")
    print()
