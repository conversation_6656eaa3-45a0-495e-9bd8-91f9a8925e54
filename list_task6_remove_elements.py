# Задача 6: Удаление элементов
# Даны два списка, удалите все элементы первого списка из второго

# Исходные списки
a = [1, 3, 4, 5]
b = [4, 5, 6, 7]

print("Список a:", a)
print("Список b:", b)

# Создаем копию списка b для работы
result = b.copy()

# Удаляем все элементы списка a из списка b
for element in a:
    while element in result:
        result.remove(element)

print("Результат после удаления элементов a из b:", result)

# Ожидаемый вывод: [6, 7]
