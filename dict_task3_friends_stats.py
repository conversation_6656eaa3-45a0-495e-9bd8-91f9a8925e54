# Задача 3: Еще немного о друзьях
# Пользователь вводит число N. Затем он вводит личные данные (имя и возраст) своих N друзей. 
# Создайте список friends и добавьте в него N словарей с ключами name и age. 
# Выведите средний возраст всех друзей и самое длинное имя.

# Ввод количества друзей
n = int(input("Введите количество друзей: "))

# Создаем пустой список для друзей
friends = []

# Вводим данные о друзьях
for i in range(n):
    print(f"\nДруг {i+1}:")
    name = input("Введите имя: ")
    age = int(input("Введите возраст: "))
    
    # Создаем словарь для друга и добавляем в список
    friend = {"name": name, "age": age}
    friends.append(friend)

# Выводим список всех друзей
print("\nСписок всех друзей:")
for friend in friends:
    print(f"{friend['name']} - {friend['age']} лет")

# Вычисляем средний возраст
total_age = sum(friend['age'] for friend in friends)
average_age = total_age / len(friends)

# Находим самое длинное имя
longest_name = friends[0]['name']  # Начинаем с первого имени
for friend in friends:
    if len(friend['name']) > len(longest_name):
        longest_name = friend['name']

# Выводим результаты
print(f"\nСредний возраст: {average_age:.0f}")
print(f"Самое длинное имя: {longest_name}")

# Альтернативный способ для поиска самого длинного имени
longest_name_alt = max(friends, key=lambda x: len(x['name']))['name']
print(f"Проверка (альтернативный способ): {longest_name_alt}")

# Дополнительная статистика
names = [friend['name'] for friend in friends]
ages = [friend['age'] for friend in friends]

print(f"\nДополнительная статистика:")
print(f"Самое короткое имя: {min(names, key=len)}")
print(f"Длина самого длинного имени: {len(longest_name)} символов")
print(f"Длина самого короткого имени: {len(min(names, key=len))} символов")
print(f"Самый старший: {max(friends, key=lambda x: x['age'])['name']} ({max(ages)} лет)")
print(f"Самый младший: {min(friends, key=lambda x: x['age'])['name']} ({min(ages)} лет)")
