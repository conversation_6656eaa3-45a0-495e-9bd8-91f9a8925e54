# Лабораторные работы по Python

Этот репозиторий содержит решения лабораторных работ по Python.

## 🚀 Быстрый запуск

### 🎯 Главное меню всех лабораторных работ:
```bash
python OMNIUM_LABORATORIUM.py
```

### Лабораторная 1: Ввод и вывод данных
```bash
python INPUT_ET_OUTPUT_DATA.py
```

### Лабораторная 2: Операторы в Python
```bash
python OPERATORES_PYTHONIS.py
```

### Лабораторная 3: Условный оператор в Python
```bash
python CONDITIONALES_PYTHONIS.py
```

### Лабораторная 4: Цикл for в Python
```bash
python CYCLUS_FOR_PYTHONIS.py
```

### Лабораторная 5: Цикл while в Python
```bash
python CYCLUS_WHILE_PYTHONIS.py
```

### Лабораторная 6: Списки в Python
```bash
python LISTA_PYTHONIS.py
```

### Лабораторная 7: Функции в Python
```bash
python FUNCTIONES_PYTHONIS.py
```

### Лабораторная 8: Строки в Python
```bash
python STRINGAE_PYTHONIS.py
```

### Лабораторная 9: Словари в Python
```bash
python DICTIONARIES_PYTHONIS.py
```

## 📚 Лабораторная работа 1: Ввод и вывод данных

### Задачи:
1. **Сумма трех** - посчитать сумму трех введенных целых чисел
2. **Площадь прямоугольника** - вычислить площадь по сторонам
3. **Периметр прямоугольника** - вычислить периметр по сторонам
4. **Площадь круга** - вычислить площадь круга по радиусу
5. **Сумма дробных** - посчитать сумму трех дробных чисел
6. **Школьники и яблоки** - разделить яблоки между школьниками

## 🔢 Лабораторная работа 2: Операторы в Python

### Задачи:
1. **Одинаковая четность** - проверить, имеют ли два числа одинаковую четность
2. **Одно положительное** - проверить, есть ли среди трех чисел хотя бы одно положительное
3. **Последняя цифра** - найти последнюю цифру натурального числа
4. **Цифры двузначного** - найти сумму цифр двузначного числа
5. **Цифры трехзначного** - найти сумму цифр трехзначного числа
6. **Разные цифры** - проверить, все ли цифры трехзначного числа различны
7. **Часы (финальный босс)** - преобразовать секунды в часы, минуты и секунды

## 🔀 Лабораторная работа 3: Условный оператор в Python

### Задачи:
1. **Меньшее из двух** - найти меньшее из двух чисел
2. **Четырехзначное?** - проверить, является ли число четырехзначным
3. **Треугольник?** - проверить, можно ли построить треугольник с заданными сторонами
4. **Время суток** - определить время суток по часам (Утро/День/Вечер/Ночь)
5. **День недели** - определить Будни или Выходные по номеру дня
6. **Описание числа** - описать число как "положительное четное число" и т.д.
7. **Конь (финальный босс)** - проверить, может ли шахматный конь сделать ход

## 🔄 Лабораторная работа 4: Цикл for в Python

### Задачи:
1. **Вывод чисел от 0 до N** - вывести все числа от 0 до N включительно
2. **Вывод чисел от K до N** - вывести все числа от K до N включительно
3. **Сумма от K до N** - найти сумму чисел от K до N включительно
4. **Сумма четных от K до N** - найти сумму только четных чисел от K до N
5. **Сумма дробей (часть первая)** - найти сумму 1 + 1.1 + 1.2 + ... + (1 + N/10)
6. **Сумма дробей (часть вторая)** - найти сумму 1 + 1/2 + 1/3 + ... + 1/N

## ⏳ Лабораторная работа 5: Цикл while в Python

### Задачи:
1. **Четные от A до B** - вывести четные числа от A до B (A > B, убывающая последовательность)
2. **От A до B на три** - вывести числа от A до B, которые делятся на три (A < B)
3. **Сумма чисел** - найти сумму чисел, вводимых до 0
4. **Максимум** - найти максимальное из чисел, вводимых до 0
5. **Минимум** - найти минимальное из чисел, вводимых до 0
6. **Факториал** - вычислить факториал числа N (N! = 1×2×3×...×N)
7. **Фибоначчи (финальный босс)** - найти N-ное число Фибоначчи

## 📋 Лабораторная работа 6: Списки в Python

### Основные задачи:
1. **Список четных чисел** - создать список из 10 четных чисел
2. **Срез списка** - работа со срезами списков
3. **Случайные числа** - добавление случайных чисел в список
4. **Очистка списка** - удаление всех элементов
5. **Удаление символов** - удаление определенных символов из списка
6. **Удаление элементов** - удаление элементов одного списка из другого
7. **Наибольший элемент** - поиск максимума в списке
8. **Наименьший элемент** - поиск минимума в списке
9. **Сумма элементов** - вычисление суммы элементов
10. **Среднее арифметическое** - вычисление среднего значения

### Сложные задачи:
11. **Локальный максимум** - поиск последнего локального максимума
12. **Одинаковые элементы** - подсчет максимального количества повторений
13. **Второй максимум** - поиск второго по величине элемента
14. **Различные элементы** - подсчет уникальных элементов

## 🔧 Лабораторная работа 7: Функции в Python

### Задачи:
1. **Площадь круга** - функция для вычисления площади круга
2. **На три** - функция проверки деления на 3
3. **Максимум в списке** - функция поиска максимального элемента
4. **Сколько четных** - функция подсчета четных элементов
5. **Уникальные** - функция получения уникальных элементов

## 📝 Лабораторная работа 8: Строки в Python

### Задачи:
1. **Длина строки** - определение длины строки
2. **Первый и последний символ** - работа с индексами
3. **Обращение строки** - переворот строки
4. **Подсчет гласных** - подсчет гласных букв
5. **Палиндром** - проверка на палиндром
6. **Количество слов** - подсчет слов в строке
7. **Замена символов** - замена пробелов на подчеркивания
8. **Поиск подстроки** - поиск позиции подстроки
9. **Удаление пробелов** - удаление всех пробелов
10. **Капитализация** - преобразование первых букв в заглавные

## 📚 Лабораторная работа 9: Словари в Python

### Задачи:
1. **Фрукты** - создать словарь фруктов с их количеством
2. **Старший и младший** - найти самого младшего друга из списка словарей
3. **Еще немного о друзьях** - найти средний возраст и самое длинное имя
4. **Английский словарь** - создать словарь английских слов с переводами

## 🛠️ Как запускать

1. Убедитесь, что Python установлен на вашем компьютере
2. Откройте терминал/командную строку
3. Перейдите в папку с файлами
4. Запустите нужную программу:
   - Для главного меню: `python OMNIUM_LABORATORIUM.py`
   - Для отдельных лабораторных: `python НАЗВАНИЕ_ФАЙЛА.py`

## 🏛️ Латинские названия

- **OMNIUM LABORATORIUM** - Все лабораторные работы
- **INPUT ET OUTPUT DATA** - Ввод и вывод данных
- **OPERATORES PYTHONIS** - Операторы Python
- **CONDITIONALES PYTHONIS** - Условные операторы Python
- **CYCLUS FOR PYTHONIS** - Цикл for Python
- **CYCLUS WHILE PYTHONIS** - Цикл while Python
- **LISTA PYTHONIS** - Списки Python
- **FUNCTIONES PYTHONIS** - Функции Python
- **STRINGAE PYTHONIS** - Строки Python
- **DICTIONARIES PYTHONIS** - Словари Python
- **LABORATORIUM OPUS** - Лабораторная работа

## 📖 Источники

- [Ввод и вывод данных](https://pymanual.github.io/input.html)
- [Операторы в Python](https://pymanual.github.io/calc.html)
- [Условный оператор в Python](https://pymanual.github.io/ifelse.html)
- [Цикл for в Python](https://pymanual.github.io/for_loop.html)
- [Цикл while в Python](https://pymanual.github.io/while_loop.html)
- [Списки в Python](https://pymanual.github.io/list.html)
- [Функции в Python](https://pymanual.github.io/func.html)
- [Словари в Python](https://pymanual.github.io/dict.html)
