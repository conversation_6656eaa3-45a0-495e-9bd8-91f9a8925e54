# Лабораторные работы по Python

Этот репозиторий содержит решения лабораторных работ по Python.

## 🚀 Быстрый запуск

### Лабораторная 1: Ввод и вывод данных
```bash
python INPUT_ET_OUTPUT_DATA.py
```

### Лабораторная 2: Операторы в Python
```bash
python OPERATORES_PYTHONIS.py
```

### Лабораторная 3: Условный оператор в Python
```bash
python CONDITIONALES_PYTHONIS.py
```

### Лабораторная 4: Цикл for в Python
```bash
python CYCLUS_FOR_PYTHONIS.py
```

### Лабораторная 5: Цикл while в Python
```bash
python CYCLUS_WHILE_PYTHONIS.py
```

## 📚 Лабораторная работа 1: Ввод и вывод данных

### Задачи:
1. **Сумма трех** - посчитать сумму трех введенных целых чисел
2. **Площадь прямоугольника** - вычислить площадь по сторонам
3. **Периметр прямоугольника** - вычислить периметр по сторонам
4. **Площадь круга** - вычислить площадь круга по радиусу
5. **Сумма дробных** - посчитать сумму трех дробных чисел
6. **Школьники и яблоки** - разделить яблоки между школьниками

## 🔢 Лабораторная работа 2: Операторы в Python

### Задачи:
1. **Одинаковая четность** - проверить, имеют ли два числа одинаковую четность
2. **Одно положительное** - проверить, есть ли среди трех чисел хотя бы одно положительное
3. **Последняя цифра** - найти последнюю цифру натурального числа
4. **Цифры двузначного** - найти сумму цифр двузначного числа
5. **Цифры трехзначного** - найти сумму цифр трехзначного числа
6. **Разные цифры** - проверить, все ли цифры трехзначного числа различны
7. **Часы (финальный босс)** - преобразовать секунды в часы, минуты и секунды

## 🔀 Лабораторная работа 3: Условный оператор в Python

### Задачи:
1. **Меньшее из двух** - найти меньшее из двух чисел
2. **Четырехзначное?** - проверить, является ли число четырехзначным
3. **Треугольник?** - проверить, можно ли построить треугольник с заданными сторонами
4. **Время суток** - определить время суток по часам (Утро/День/Вечер/Ночь)
5. **День недели** - определить Будни или Выходные по номеру дня
6. **Описание числа** - описать число как "положительное четное число" и т.д.
7. **Конь (финальный босс)** - проверить, может ли шахматный конь сделать ход

## 🔄 Лабораторная работа 4: Цикл for в Python

### Задачи:
1. **Вывод чисел от 0 до N** - вывести все числа от 0 до N включительно
2. **Вывод чисел от K до N** - вывести все числа от K до N включительно
3. **Сумма от K до N** - найти сумму чисел от K до N включительно
4. **Сумма четных от K до N** - найти сумму только четных чисел от K до N
5. **Сумма дробей (часть первая)** - найти сумму 1 + 1.1 + 1.2 + ... + (1 + N/10)
6. **Сумма дробей (часть вторая)** - найти сумму 1 + 1/2 + 1/3 + ... + 1/N

## ⏳ Лабораторная работа 5: Цикл while в Python

### Задачи:
1. **Четные от A до B** - вывести четные числа от A до B (A > B, убывающая последовательность)
2. **От A до B на три** - вывести числа от A до B, которые делятся на три (A < B)
3. **Сумма чисел** - найти сумму чисел, вводимых до 0
4. **Максимум** - найти максимальное из чисел, вводимых до 0
5. **Минимум** - найти минимальное из чисел, вводимых до 0
6. **Факториал** - вычислить факториал числа N (N! = 1×2×3×...×N)
7. **Фибоначчи (финальный босс)** - найти N-ное число Фибоначчи

## 📁 Отдельные файлы

### Лабораторная 1:
- `task1_sum_three.py` - `task6_students_apples.py`

### Лабораторная 2:
- `calc_task1_same_parity.py` - `calc_task7_clock.py`

### Лабораторная 3:
- `ifelse_task1_min_of_two.py` - `ifelse_task7_knight.py`

### Лабораторная 4:
- `for_task1_numbers_0_to_n.py` - `for_task6_sum_fractions_2.py`

### Лабораторная 5:
- `while_task1_even_a_to_b.py` - `while_task7_fibonacci.py`

## 🎯 Примеры использования

### Задача "Треугольник":
```
Ввод: 3, 4, 5
Вывод: YES

Ввод: 3, 10, 5
Вывод: NO
```

### Задача "Конь":
```
Ввод: 1, 1, 3, 2
Вывод: YES

Ввод: 1, 1, 2, 2
Вывод: NO
```

### Задача "Сумма от K до N":
```
Ввод: 1, 5
Вывод: 15 (1+2+3+4+5)
```

### Задача "Сумма дробей":
```
Ввод: 5
Вывод: 2.283 (1 + 1/2 + 1/3 + 1/4 + 1/5)
```

### Задача "Факториал":
```
Ввод: 5
Вывод: 120 (5! = 1×2×3×4×5)
```

### Задача "Фибоначчи":
```
Ввод: 10
Вывод: 55 (10-е число Фибоначчи)
```

## 📖 Источники

- [Ввод и вывод данных](https://pymanual.github.io/input.html)
- [Операторы в Python](https://pymanual.github.io/calc.html)
- [Условный оператор в Python](https://pymanual.github.io/ifelse.html)
- [Цикл for в Python](https://pymanual.github.io/for_loop.html)
- [Цикл while в Python](https://pymanual.github.io/while_loop.html)

## 🛠️ Как запускать

1. Убедитесь, что Python установлен на вашем компьютере
2. Откройте терминал/командную строку
3. Перейдите в папку с файлами
4. Запустите нужную программу:
   - Для объединенных файлов: `python НАЗВАНИЕ_ФАЙЛА.py`
   - Для отдельных задач: `python имя_файла.py`

## 🏛️ Латинские названия

- **INPUT ET OUTPUT DATA** - Ввод и вывод данных
- **OPERATORES PYTHONIS** - Операторы Python
- **CONDITIONALES PYTHONIS** - Условные операторы Python
- **CYCLUS FOR PYTHONIS** - Цикл for Python
- **CYCLUS WHILE PYTHONIS** - Цикл while Python
- **LABORATORIUM OPUS** - Лабораторная работа
