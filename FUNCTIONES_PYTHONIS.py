# ================================================================
# ЛАБОРАТОРНАЯ РАБОТА: ФУНКЦИИ В PYTHON
# LABORATORIUM OPUS: FUNCTIONES PYTHONIS
# ================================================================
# Все задачи в одном файле
# Omnes exercitationes in uno archivo

def circle(radius):
    """Задача 1: Функция для вычисления площади круга по радиусу"""
    pi = 3.14
    area = pi * radius * radius
    return area

def three(number):
    """Задача 2: Функция для проверки, делится ли число на 3"""
    return number % 3 == 0

def maxList(numbers):
    """Задача 3: Функция для поиска максимального элемента в списке"""
    if not numbers:  # Проверка на пустой список
        return None
    
    max_element = numbers[0]
    for number in numbers:
        if number > max_element:
            max_element = number
    return max_element

def evenCounter(numbers):
    """Задача 4: Функция для подсчета количества четных элементов в списке"""
    count = 0
    for number in numbers:
        if number % 2 == 0:
            count += 1
    return count

def unique(numbers):
    """Задача 5: Функция для получения списка с уникальными элементами"""
    unique_list = []
    for number in numbers:
        if number not in unique_list:
            unique_list.append(number)
    return unique_list

def test_task1():
    """Тестирование задачи 1: Площадь круга"""
    print("\n=== Задача 1: Площадь круга ===")
    print(f"circle(4) = {circle(4)}")  # Ожидается: 50.24
    print(f"circle(1) = {circle(1)}")  # Ожидается: 3.14
    print(f"circle(2) = {circle(2)}")  # Ожидается: 12.56

def test_task2():
    """Тестирование задачи 2: На три"""
    print("\n=== Задача 2: На три ===")
    test_numbers = [9, 10, 0, 15, 7, 12]
    for num in test_numbers:
        result = three(num)
        print(f"three({num}) = {result}")

def test_task3():
    """Тестирование задачи 3: Максимум в списке"""
    print("\n=== Задача 3: Максимум в списке ===")
    test_lists = [
        [1, 2, 3, 4, 5],
        [10, 5, 8, 3, 9],
        [-1, -5, -3, -2],
        [42]
    ]
    for lst in test_lists:
        result = maxList(lst)
        print(f"maxList({lst}) = {result}")

def test_task4():
    """Тестирование задачи 4: Сколько четных"""
    print("\n=== Задача 4: Сколько четных ===")
    test_lists = [
        [1, 2, 3, 4, 5],
        [2, 4, 6, 8],
        [1, 3, 5, 7],
        [0, 10, 15, 20, 25]
    ]
    for lst in test_lists:
        result = evenCounter(lst)
        print(f"evenCounter({lst}) = {result}")

def test_task5():
    """Тестирование задачи 5: Уникальные"""
    print("\n=== Задача 5: Уникальные ===")
    test_lists = [
        [1, 2, 2, 3, 3, 3, 4],
        [5, 5, 5, 5],
        [1, 2, 3, 4, 5],
        [7, 3, 7, 1, 3, 9, 1]
    ]
    for lst in test_lists:
        result = unique(lst)
        print(f"unique({lst}) = {result}")

def show_menu():
    """Показать меню выбора задач"""
    print("\n" + "="*50)
    print("ЛАБОРАТОРНАЯ РАБОТА: ФУНКЦИИ В PYTHON")
    print("FUNCTIONES PYTHONIS - LABORATORIUM OPUS")
    print("="*50)
    print("Выберите задачу для тестирования:")
    print("1. Площадь круга")
    print("2. На три")
    print("3. Максимум в списке")
    print("4. Сколько четных")
    print("5. Уникальные")
    print("6. Тестировать все функции")
    print("0. Выход")
    print("="*50)

def test_all():
    """Тестирование всех функций"""
    print("\n=== ТЕСТИРОВАНИЕ ВСЕХ ФУНКЦИЙ ===")
    test_task1()
    test_task2()
    test_task3()
    test_task4()
    test_task5()

def main():
    """Основная функция программы"""
    tasks = {
        1: test_task1,
        2: test_task2,
        3: test_task3,
        4: test_task4,
        5: test_task5,
        6: test_all
    }
    
    while True:
        show_menu()
        try:
            choice = int(input("Введите номер задачи (0 для выхода): "))
            
            if choice == 0:
                print("Спасибо за использование программы!")
                break
            elif choice in tasks:
                tasks[choice]()
                input("\nНажмите Enter для продолжения...")
            else:
                print("Неверный выбор! Пожалуйста, выберите номер от 0 до 6.")
        except ValueError:
            print("Ошибка! Введите число от 0 до 6.")
        except KeyboardInterrupt:
            print("\n\nПрограмма завершена пользователем.")
            break

if __name__ == "__main__":
    main()
