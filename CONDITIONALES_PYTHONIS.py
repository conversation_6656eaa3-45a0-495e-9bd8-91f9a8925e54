# ================================================================
# ЛАБОРАТОРНАЯ РАБОТА: УСЛОВНЫЙ ОПЕРАТОР
# ================================================================

def task1_min_of_two():
    """Задача 1: Меньшее из двух"""
    print("\n=== Задача 1: Меньшее из двух ===")
    a = int(input('Введите первое число: '))
    b = int(input('Введите второе число: '))
    if a < b:
        print(f"Меньшее число: {a}")
    else:
        print(f"Меньшее число: {b}")

def task2_four_digit():
    """Задача 2: Четырехзначное?"""
    print("\n=== Задача 2: Четырехзначное? ===")
    number = int(input('Введите целое число: '))
    if (1000 <= number <= 9999) or (-9999 <= number <= -1000):
        print("YES")
    else:
        print("NO")

def task3_triangle():
    """Задача 3: Треугольник?"""
    print("\n=== Задача 3: Треугольник? ===")
    a = int(input('Введите первую сторону: '))
    b = int(input('Введите вторую сторону: '))
    c = int(input('Введите третью сторону: '))
    if (a + b > c) and (a + c > b) and (b + c > a):
        print("YES")
    else:
        print("NO")

def task4_time_of_day():
    """Задача 4: Время суток"""
    print("\n=== Задача 4: Время суток ===")
    hour = int(input('Введите час (0-23): '))
    if 5 <= hour <= 11:
        print("Утро")
    elif 12 <= hour <= 17:
        print("День")
    elif 18 <= hour <= 22:
        print("Вечер")
    elif (23 <= hour <= 23) or (0 <= hour <= 4):
        print("Ночь")
    else:
        print("Ошибка")

def task5_day_of_week():
    """Задача 5: День недели"""
    print("\n=== Задача 5: День недели ===")
    day = int(input('Введите номер дня недели (1-7): '))
    if 1 <= day <= 5:
        print("Будни")
    elif day == 6 or day == 7:
        print("Выходные")

def task6_number_description():
    """Задача 6: Описание числа"""
    print("\n=== Задача 6: Описание числа ===")
    number = int(input('Введите целое число: '))
    if number == 0:
        print("ноль")
    elif number > 0:
        if number % 2 == 0:
            print("положительное четное число")
        else:
            print("положительное нечетное число")
    else:
        if number % 2 == 0:
            print("отрицательное четное число")
        else:
            print("отрицательное нечетное число")

def task7_knight():
    """Задача 7: Конь (финальный босс)"""
    print("\n=== Задача 7: Конь (финальный босс) ===")
    print("Введите координаты первой клетки:")
    x1 = int(input('x1: '))
    y1 = int(input('y1: '))
    print("Введите координаты второй клетки:")
    x2 = int(input('x2: '))
    y2 = int(input('y2: '))
    
    if not (1 <= x1 <= 8 and 1 <= y1 <= 8 and 1 <= x2 <= 8 and 1 <= y2 <= 8):
        print("Ошибка!")
    else:
        dx = abs(x2 - x1)
        dy = abs(y2 - y1)
        if (dx == 2 and dy == 1) or (dx == 1 and dy == 2):
            print("YES")
        else:
            print("NO")

def show_menu():
    """Показать меню выбора задач"""
    print("\n" + "="*55)
    print("ЛАБОРАТОРНАЯ РАБОТА: УСЛОВНЫЙ ОПЕРАТОР В PYTHON")
    print("CONDITIONALES PYTHONIS - LABORATORIUM OPUS")
    print("="*55)
    print("Выберите задачу:")
    print("1. Меньшее из двух")
    print("2. Четырехзначное?")
    print("3. Треугольник?")
    print("4. Время суток")
    print("5. День недели")
    print("6. Описание числа")
    print("7. Конь (финальный босс)")
    print("0. Выход")
    print("="*55)

def main():
    """Основная функция программы"""
    tasks = {
        1: task1_min_of_two,
        2: task2_four_digit,
        3: task3_triangle,
        4: task4_time_of_day,
        5: task5_day_of_week,
        6: task6_number_description,
        7: task7_knight
    }
    
    while True:
        show_menu()
        try:
            choice = int(input("Введите номер задачи (0 для выхода): "))
            
            if choice == 0:
                print("Вы вышли из программы)")
                break
            elif choice in tasks:
                tasks[choice]()
                input("\nНажмите Enter для продолжения...")
            else:
                print("Неверный выбор! Пожалуйста, выберите номер от 0 до 7.")
        except ValueError:
            print("Ошибка! Введите число от 0 до 7.")
        except KeyboardInterrupt:
            print("\n\nПрограмма завершена пользователем.")
            break

if __name__ == "__main__":
    main()
