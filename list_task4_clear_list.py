# Задача 4: Очистка списка
# Удалите все элементы из списка, созданного в задании 3

from random import randint

# Создаем пустой список и добавляем случайные числа (как в задании 3)
random_numbers = []
for i in range(10):
    random_number = randint(1, 100)
    random_numbers.append(random_number)

print("Список до очистки:")
print(random_numbers)
print(f"Длина списка: {len(random_numbers)}")

# Удаляем все элементы из списка
random_numbers.clear()

print("Список после очистки:")
print(random_numbers)
print(f"Длина списка: {len(random_numbers)}")
