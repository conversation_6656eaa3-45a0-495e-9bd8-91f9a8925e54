# ================================================================
# ЛАБОРАТОРНАЯ РАБОТА: ОПЕРАТОРЫ
# ================================================================

def task1_same_parity():
    """Задача 1: Одинаковая четность"""
    print("\n=== Задача 1: Одинаковая четность ===")
    A = int(input('Введите первое число: '))
    B = int(input('Введите второе число: '))
    same_parity = (A % 2) == (B % 2)
    print(f"Результат: {same_parity}")

def task2_one_positive():
    """Задача 2: Одно положительное"""
    print("\n=== Задача 2: Одно положительное ===")
    A = int(input('Введите первое число: '))
    B = int(input('Введите второе число: '))
    C = int(input('Введите третье число: '))
    at_least_one_positive = (A > 0) or (B > 0) or (C > 0)
    print(f"Результат: {at_least_one_positive}")

def task3_last_digit():
    """Задача 3: Последняя цифра"""
    print("\n=== Задача 3: Последняя цифра ===")
    number = int(input('Введите натуральное число: '))
    last_digit = number % 10
    print(f"Последняя цифра: {last_digit}")

def task4_two_digit_sum():
    """Задача 4: Цифры двузначного"""
    print("\n=== Задача 4: Цифры двузначного ===")
    number = int(input('Введите двузначное число: '))
    first_digit = number // 10
    second_digit = number % 10
    digit_sum = first_digit + second_digit
    print(f"Сумма цифр: {digit_sum}")

def task5_three_digit_sum():
    """Задача 5: Цифры трехзначного"""
    print("\n=== Задача 5: Цифры трехзначного ===")
    number = int(input('Введите трехзначное число: '))
    first_digit = number // 100
    second_digit = (number // 10) % 10
    third_digit = number % 10
    digit_sum = first_digit + second_digit + third_digit
    print(f"Сумма цифр: {digit_sum}")

def task6_different_digits():
    """Задача 6: Разные цифры"""
    print("\n=== Задача 6: Разные цифры ===")
    number = int(input('Введите трехзначное число: '))
    first_digit = number // 100
    second_digit = (number // 10) % 10
    third_digit = number % 10
    all_different = (first_digit != second_digit) and (first_digit != third_digit) and (second_digit != third_digit)
    print(f"Все цифры различны: {all_different}")

def task7_clock():
    """Задача 7: Часы (финальный босс)"""
    print("\n=== Задача 7: Часы (финальный босс) ===")
    N = int(input('Введите количество секунд с начала суток: '))
    hours = N // 3600
    remaining_seconds = N % 3600
    minutes = remaining_seconds // 60
    seconds = remaining_seconds % 60
    print(f"Время: {hours} часов {minutes} минут {seconds} секунд")
    print(f"Формат вывода: {hours} {minutes} {seconds}")

def show_menu():
    """Показать меню выбора задач"""
    print("\n" + "="*50)
    print("ЛАБОРАТОРНАЯ РАБОТА: ОПЕРАТОРЫ В PYTHON")
    print("OPERATORES PYTHONIS - LABORATORIUM OPUS")
    print("="*50)
    print("Выберите задачу:")
    print("1. Одинаковая четность")
    print("2. Одно положительное")
    print("3. Последняя цифра")
    print("4. Цифры двузначного")
    print("5. Цифры трехзначного")
    print("6. Разные цифры")
    print("7. Часы (финальный босс)")
    print("0. Выход")
    print("="*50)

def main():
    """Основная функция программы"""
    tasks = {
        1: task1_same_parity,
        2: task2_one_positive,
        3: task3_last_digit,
        4: task4_two_digit_sum,
        5: task5_three_digit_sum,
        6: task6_different_digits,
        7: task7_clock
    }
    
    while True:
        show_menu()
        try:
            choice = int(input("Введите номер задачи (0 для выхода): "))
            
            if choice == 0:
                print("Вы вышли из программы)")
                break
            elif choice in tasks:
                tasks[choice]()
                input("\nНажмите Enter для продолжения...")
            else:
                print("Неверный выбор! Пожалуйста, выберите номер от 0 до 7.")
        except ValueError:
            print("Ошибка! Введите число от 0 до 7.")
        except KeyboardInterrupt:
            print("\n\nПрограмма завершена пользователем.")
            break

if __name__ == "__main__":
    main()
