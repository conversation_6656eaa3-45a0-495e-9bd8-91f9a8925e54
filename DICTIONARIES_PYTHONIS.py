# ================================================================
# ЛАБОРАТОРНАЯ РАБОТА: СЛОВАРИ В PYTHON
# LABORATORIUM OPUS: DICTIONARIES PYTHONIS
# ================================================================
# Все задачи в одном файле
# Omnes exercitationes in uno archivo

def task1_fruits():
    """Задача 1: Фрукты"""
    print("\n=== Задача 1: Фрукты ===")
    k = int(input("Введите количество фруктов: "))
    
    fruits = {}
    for i in range(k):
        fruit_name = input(f"Введите название {i+1}-го фрукта: ")
        fruit_count = int(input(f"Введите количество {fruit_name}: "))
        fruits[fruit_name] = fruit_count
    
    print("Словарь фруктов:")
    print(fruits)
    
    print(f"\nВсего видов фруктов: {len(fruits)}")
    total_fruits = sum(fruits.values())
    print(f"Общее количество фруктов: {total_fruits}")

def task2_youngest_friend():
    """Задача 2: Старший и младший"""
    print("\n=== Задача 2: Старший и младший ===")
    n = int(input("Введите количество друзей: "))
    
    friends = []
    for i in range(n):
        print(f"\nДруг {i+1}:")
        name = input("Введите имя: ")
        age = int(input("Введите возраст: "))
        friends.append({"name": name, "age": age})
    
    print("\nСписок всех друзей:")
    for friend in friends:
        print(f"{friend['name']} - {friend['age']} лет")
    
    youngest_friend = min(friends, key=lambda x: x['age'])
    print(f"\nСамый младший друг: {youngest_friend['name']}")

def task3_friends_stats():
    """Задача 3: Еще немного о друзьях"""
    print("\n=== Задача 3: Еще немного о друзьях ===")
    n = int(input("Введите количество друзей: "))
    
    friends = []
    for i in range(n):
        print(f"\nДруг {i+1}:")
        name = input("Введите имя: ")
        age = int(input("Введите возраст: "))
        friends.append({"name": name, "age": age})
    
    print("\nСписок всех друзей:")
    for friend in friends:
        print(f"{friend['name']} - {friend['age']} лет")
    
    # Средний возраст
    average_age = sum(friend['age'] for friend in friends) / len(friends)
    
    # Самое длинное имя
    longest_name = max(friends, key=lambda x: len(x['name']))['name']
    
    print(f"\nСредний возраст: {average_age:.0f}")
    print(f"Самое длинное имя: {longest_name}")

def task4_english_dictionary():
    """Задача 4: Английский словарь"""
    print("\n=== Задача 4: Английский словарь ===")
    n = int(input("Введите количество слов: "))
    
    english_dict = {}
    for i in range(n):
        print(f"\nСлово {i+1}:")
        line = input("Введите строку в формате 'english_word - перевод1, перевод2, ...': ")
        
        parts = line.split(' - ')
        english_word = parts[0].strip()
        translations_str = parts[1].strip()
        translations = [translation.strip() for translation in translations_str.split(',')]
        
        english_dict[english_word] = translations
    
    print("\nАнглийский словарь:")
    print(english_dict)
    
    print("\nСловарь в удобном формате:")
    for english, russian_list in english_dict.items():
        print(f"{english}: {', '.join(russian_list)}")

def demo_dictionaries():
    """Демонстрация основных операций со словарями"""
    print("\n=== Демонстрация работы со словарями ===")
    
    # Создание словаря
    student = {'name': 'Иван', 'age': 20, 'course': 2}
    print("Исходный словарь:", student)
    
    # Доступ к элементам
    print(f"Имя студента: {student['name']}")
    print(f"Возраст: {student.get('age', 'Неизвестно')}")
    
    # Добавление нового ключа
    student['university'] = 'МГУ'
    print("После добавления университета:", student)
    
    # Изменение значения
    student['age'] = 21
    print("После изменения возраста:", student)
    
    # Удаление ключа
    removed_course = student.pop('course')
    print(f"Удален курс: {removed_course}")
    print("После удаления курса:", student)
    
    # Методы словаря
    print("Ключи:", list(student.keys()))
    print("Значения:", list(student.values()))
    print("Пары ключ-значение:", list(student.items()))

def show_menu():
    """Показать меню выбора задач"""
    print("\n" + "="*50)
    print("ЛАБОРАТОРНАЯ РАБОТА: СЛОВАРИ В PYTHON")
    print("DICTIONARIES PYTHONIS - LABORATORIUM OPUS")
    print("="*50)
    print("Выберите задачу:")
    print("1. Фрукты")
    print("2. Старший и младший")
    print("3. Еще немного о друзьях")
    print("4. Английский словарь")
    print("5. Демонстрация работы со словарями")
    print("0. Выход")
    print("="*50)

def main():
    """Основная функция программы"""
    tasks = {
        1: task1_fruits,
        2: task2_youngest_friend,
        3: task3_friends_stats,
        4: task4_english_dictionary,
        5: demo_dictionaries
    }
    
    while True:
        show_menu()
        try:
            choice = int(input("Введите номер задачи (0 для выхода): "))
            
            if choice == 0:
                print("Спасибо за использование программы!")
                break
            elif choice in tasks:
                tasks[choice]()
                input("\nНажмите Enter для продолжения...")
            else:
                print("Неверный выбор! Выберите номер от 0 до 5.")
        except ValueError:
            print("Ошибка! Введите число от 0 до 5.")
        except KeyboardInterrupt:
            print("\n\nПрограмма завершена пользователем.")
            break

if __name__ == "__main__":
    main()
