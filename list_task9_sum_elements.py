# Задача 9: Сумма элементов
# Найдите сумму элементов списка из задания 7

from random import randint

# Создаем список из случайных чисел (как в задании 7)
random_numbers = []
for i in range(10):
    random_number = randint(1, 100)
    random_numbers.append(random_number)

print("Список случайных чисел:")
print(random_numbers)

# Находим сумму элементов
total_sum = sum(random_numbers)

print(f"Сумма элементов: {total_sum}")

# Альтернативный способ через цикл:
sum_manual = 0
for number in random_numbers:
    sum_manual += number

print(f"Сумма элементов (вычислена вручную): {sum_manual}")
