# Задача 8: Наименьший элемент
# Найдите наименьший элемент в списке из задания 7

from random import randint

# Создаем список из случайных чисел (как в задании 7)
random_numbers = []
for i in range(10):
    random_number = randint(1, 100)
    random_numbers.append(random_number)

print("Список случайных чисел:")
print(random_numbers)

# Находим наименьший элемент
min_element = min(random_numbers)

print(f"Наименьший элемент: {min_element}")

# Альтернативный способ через цикл:
min_manual = random_numbers[0]
for number in random_numbers:
    if number < min_manual:
        min_manual = number

print(f"Наименьший элемент (найден вручную): {min_manual}")
