# ================================================================
# ЛАБОРАТОРНАЯ РАБОТА: ЦИКЛ WHILE
# ================================================================

def task1_even_a_to_b():
    """Задача 1: Четные от A до B"""
    print("\n=== Задача 1: Четные от A до B ===")
    A = int(input('Введите число A (большее): '))
    B = int(input('Введите число B (меньшее): '))
    print(f"Четные числа от {A} до {B}:")
    while A >= B:
        if A % 2 == 0:
            print(A)
        A -= 1

def task2_divisible_by_three():
    """Задача 2: От A до B на три"""
    print("\n=== Задача 2: От A до B на три ===")
    A = int(input('Введите число A (меньшее): '))
    B = int(input('Введите число B (большее): '))
    print(f"Числа от {A} до {B}, которые делятся на 3:")
    while A <= B:
        if A % 3 == 0:
            print(A)
        A += 1

def task3_sum_numbers():
    """Задача 3: Сумма чисел"""
    print("\n=== Задача 3: Сумма чисел ===")
    print("Вводите числа (0 для завершения):")
    total_sum = 0
    number = int(input())
    while number != 0:
        total_sum += number
        number = int(input())
    print(f"Сумма равна: {total_sum}")

def task4_maximum():
    """Задача 4: Максимум"""
    print("\n=== Задача 4: Максимум ===")
    print("Вводите числа (0 для завершения):")
    number = int(input())
    maximum = number
    while number != 0:
        if number > maximum:
            maximum = number
        number = int(input())
    print(f"Максимум равен: {maximum}")

def task5_minimum():
    """Задача 5: Минимум"""
    print("\n=== Задача 5: Минимум ===")
    print("Вводите числа (0 для завершения):")
    number = int(input())
    minimum = number
    while number != 0:
        if number < minimum:
            minimum = number
        number = int(input())
    print(f"Минимум равен: {minimum}")

def task6_factorial():
    """Задача 6: Факториал"""
    print("\n=== Задача 6: Факториал ===")
    N = int(input('Введите число N: '))
    factorial = 1
    i = 1
    while i <= N:
        factorial *= i
        i += 1
    print(f"Факториал {N}! = {factorial}")

def task7_fibonacci():
    """Задача 7: Фибоначчи (финальный босс)"""
    print("\n=== Задача 7: Фибоначчи (финальный босс) ===")
    N = int(input('Введите номер числа Фибоначчи: '))
    if N == 1 or N == 2:
        result = 1
    else:
        fib1 = 1
        fib2 = 1
        i = 3
        while i <= N:
            fib_next = fib1 + fib2
            fib1 = fib2
            fib2 = fib_next
            i += 1
        result = fib2
    print(f"F({N}) = {result}")

def show_menu():
    """Показать меню выбора задач"""
    print("\n" + "="*50)
    print("ЛАБОРАТОРНАЯ РАБОТА: ЦИКЛ WHILE В PYTHON")
    print("CYCLUS WHILE PYTHONIS - LABORATORIUM OPUS")
    print("="*50)
    print("Выберите задачу:")
    print("1. Четные от A до B")
    print("2. От A до B на три")
    print("3. Сумма чисел")
    print("4. Максимум")
    print("5. Минимум")
    print("6. Факториал")
    print("7. Фибоначчи (финальный босс)")
    print("0. Выход")
    print("="*50)

def main():
    """Основная функция программы"""
    tasks = {
        1: task1_even_a_to_b,
        2: task2_divisible_by_three,
        3: task3_sum_numbers,
        4: task4_maximum,
        5: task5_minimum,
        6: task6_factorial,
        7: task7_fibonacci
    }
    
    while True:
        show_menu()
        try:
            choice = int(input("Введите номер задачи (0 для выхода): "))
            
            if choice == 0:
                print("Вы вышли из программы)")
                break
            elif choice in tasks:
                tasks[choice]()
                input("\nНажмите Enter для продолжения...")
            else:
                print("Неверный выбор! Пожалуйста, выберите номер от 0 до 7.")
        except ValueError:
            print("Ошибка! Введите число от 0 до 7.")
        except KeyboardInterrupt:
            print("\n\nПрограмма завершена пользователем.")
            break

if __name__ == "__main__":
    main()
