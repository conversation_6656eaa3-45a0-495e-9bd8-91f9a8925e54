# Лабораторная работа: Ввод и вывод данных
# Для запуска всех задач в одном файле: python INPUT_ET_OUTPUT_DATA.py

def task1_sum_three():
    """Задача 1: Сумма трех"""
    print("\n=== Задача 1: Сумма трех ===")
    number1 = int(input('Введите первое число: '))
    number2 = int(input('Введите второе число: '))
    number3 = int(input('Введите третье число: '))
    sum_result = number1 + number2 + number3
    print(f"Сумма: {sum_result}")

def task2_rectangle_area():
    """Задача 2: Площадь прямоугольника"""
    print("\n=== Задача 2: Площадь прямоугольника ===")
    length = int(input('Введите длину прямоугольника: '))
    width = int(input('Введите ширину прямоугольника: '))
    area = length * width
    print(f"Площадь: {area}")

def task3_rectangle_perimeter():
    """Задача 3: Периметр прямоугольника"""
    print("\n=== Задача 3: Периметр прямоугольника ===")
    length = int(input('Введите длину прямоугольника: '))
    width = int(input('Введите ширину прямоугольника: '))
    perimeter = 2 * (length + width)
    print(f"Периметр: {perimeter}")

def task4_circle_area():
    """Задача 4: Площадь круга"""
    print("\n=== Задача 4: Площадь круга ===")
    radius = int(input('Введите радиус круга: '))
    pi = 3.14
    area = pi * radius * radius
    print(f"Площадь: {area}")

def task5_sum_floats():
    """Задача 5: Сумма дробных"""
    print("\n=== Задача 5: Сумма дробных ===")
    number1 = float(input('Введите первое дробное число: '))
    number2 = float(input('Введите второе дробное число: '))
    number3 = float(input('Введите третье дробное число: '))
    sum_result = number1 + number2 + number3
    print(f"Сумма: {sum_result}")

def task6_students_apples():
    """Задача 6: Школьники и яблоки"""
    print("\n=== Задача 6: Школьники и яблоки ===")
    k = int(input('Введите количество яблок: '))
    n = int(input('Введите количество школьников: '))
    apples_per_student = k // n
    apples_remaining = k % n
    print(f"Каждому школьнику: {apples_per_student}")
    print(f"Останется в корзинке: {apples_remaining}")

def show_menu():
    """Показать меню выбора задач"""
    print("\n" + "="*50)
    print("ЛАБОРАТОРНАЯ РАБОТА: ВВОД И ВЫВОД ДАННЫХ")
    print("="*50)
    print("Выберите задачу:")
    print("1. Сумма трех чисел")
    print("2. Площадь прямоугольника")
    print("3. Периметр прямоугольника")
    print("4. Площадь круга")
    print("5. Сумма дробных чисел")
    print("6. Школьники и яблоки")
    print("0. Выход")
    print("="*50)

def main():
    """Основная функция программы"""
    tasks = {
        1: task1_sum_three,
        2: task2_rectangle_area,
        3: task3_rectangle_perimeter,
        4: task4_circle_area,
        5: task5_sum_floats,
        6: task6_students_apples
    }
    
    while True:
        show_menu()
        try:
            choice = int(input("Введите номер задачи (0 для выхода): "))
            
            if choice == 0:
                print("Вы вышли из программы)")
                break
            elif choice in tasks:
                tasks[choice]()
                input("\nНажмите Enter для продолжения...")
            else:
                print("Неверный выбор! Пожалуйста, выберите номер от 0 до 6.")
        except ValueError:
            print("Ошибка! Введите число от 0 до 6.")
        except KeyboardInterrupt:
            print("\n\nПрограмма завершена пользователем.")
            break

if __name__ == "__main__":
    main()
