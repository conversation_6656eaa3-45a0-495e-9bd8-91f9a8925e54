# ================================================================
# ЛАБОРАТОРНАЯ РАБОТА: СПИСКИ В PYTHON
# LABORATORIUM OPUS: LISTA PYTHONIS
# ================================================================
# Все задачи в одном файле
# Omnes exercitationes in uno archivo

from random import randint

def task1_even_numbers():
    """Задача 1: Список четных чисел"""
    print("\n=== Задача 1: Список четных чисел ===")
    even_numbers = list(range(0, 20, 2))
    print("Список четных чисел:")
    for number in even_numbers:
        print(number, end=' ')
    print()

def task2_slice():
    """Задача 2: Срез списка"""
    print("\n=== Задача 2: Срез списка ===")
    my_list = ['apple', 'banana', 'cherry', 'date', 'elderberry']
    print("Исходный список:", my_list)
    slice_result = my_list[2:4]
    print("Срез от второго индекса до четвертого:", slice_result)

def task3_random_numbers():
    """Задача 3: Случайные числа"""
    print("\n=== Задача 3: Случайные числа ===")
    random_numbers = []
    for i in range(10):
        random_numbers.append(randint(1, 100))
    print("Список случайных чисел:", random_numbers)
    return random_numbers

def task4_clear_list():
    """Задача 4: Очистка списка"""
    print("\n=== Задача 4: Очистка списка ===")
    numbers = task3_random_numbers()
    print(f"Длина до очистки: {len(numbers)}")
    numbers.clear()
    print(f"Список после очистки: {numbers}")
    print(f"Длина после очистки: {len(numbers)}")

def task5_remove_vowels():
    """Задача 5: Удаление символов"""
    print("\n=== Задача 5: Удаление символов ===")
    user_string = input("Введите строку: ")
    char_list = list(user_string)
    print("Исходный список:", char_list)
    
    vowels = ['a', 'e', 'o']
    for vowel in vowels:
        while vowel in char_list:
            char_list.remove(vowel)
    
    print("После удаления 'a', 'e', 'o':", char_list)
    print("Результирующая строка:", ''.join(char_list))

def task6_remove_elements():
    """Задача 6: Удаление элементов"""
    print("\n=== Задача 6: Удаление элементов ===")
    a = [1, 3, 4, 5]
    b = [4, 5, 6, 7]
    print("Список a:", a)
    print("Список b:", b)
    
    result = b.copy()
    for element in a:
        while element in result:
            result.remove(element)
    
    print("Результат:", result)

def task7_max_element():
    """Задача 7: Наибольший элемент"""
    print("\n=== Задача 7: Наибольший элемент ===")
    numbers = [randint(1, 100) for _ in range(10)]
    print("Список:", numbers)
    print("Наибольший элемент:", max(numbers))
    return numbers

def task8_min_element():
    """Задача 8: Наименьший элемент"""
    print("\n=== Задача 8: Наименьший элемент ===")
    numbers = [randint(1, 100) for _ in range(10)]
    print("Список:", numbers)
    print("Наименьший элемент:", min(numbers))

def task9_sum_elements():
    """Задача 9: Сумма элементов"""
    print("\n=== Задача 9: Сумма элементов ===")
    numbers = [randint(1, 100) for _ in range(10)]
    print("Список:", numbers)
    print("Сумма элементов:", sum(numbers))

def task10_average():
    """Задача 10: Среднее арифметическое"""
    print("\n=== Задача 10: Среднее арифметическое ===")
    numbers = [randint(1, 100) for _ in range(10)]
    print("Список:", numbers)
    average = sum(numbers) / len(numbers)
    print(f"Среднее арифметическое: {average:.2f}")

def hard_task1_local_maximum():
    """Сложная задача 1: Локальный максимум"""
    print("\n=== Сложная задача 1: Локальный максимум ===")
    numbers = [randint(1, 20) for _ in range(10)]
    print("Список:", numbers)
    
    last_local_max = -1
    for i in range(1, len(numbers) - 1):
        if numbers[i] > numbers[i-1] and numbers[i] > numbers[i+1]:
            last_local_max = i
    
    if last_local_max != -1:
        print(f"Последний локальный максимум: индекс {last_local_max}, значение {numbers[last_local_max]}")
    else:
        print("Локальных максимумов не найдено")

def hard_task2_max_duplicates():
    """Сложная задача 2: Одинаковые элементы"""
    print("\n=== Сложная задача 2: Одинаковые элементы ===")
    numbers = [randint(1, 10) for _ in range(15)]
    print("Список:", numbers)
    
    counts = {}
    for num in numbers:
        counts[num] = counts.get(num, 0) + 1
    
    max_count = max(counts.values())
    print(f"Максимальное количество одинаковых элементов: {max_count}")

def hard_task3_second_max():
    """Сложная задача 3: Второй максимум"""
    print("\n=== Сложная задача 3: Второй максимум ===")
    numbers = [randint(1, 50) for _ in range(10)]
    print("Список:", numbers)
    
    unique_sorted = sorted(set(numbers), reverse=True)
    if len(unique_sorted) >= 2:
        print(f"Второй максимум: {unique_sorted[1]}")
    else:
        print("Недостаточно различных элементов")

def hard_task4_unique_count():
    """Сложная задача 4: Различные элементы"""
    print("\n=== Сложная задача 4: Различные элементы ===")
    numbers = [randint(1, 10) for _ in range(15)]
    print("Список:", numbers)
    unique_count = len(set(numbers))
    print(f"Количество различных элементов: {unique_count}")

def show_menu():
    """Показать меню выбора задач"""
    print("\n" + "="*50)
    print("ЛАБОРАТОРНАЯ РАБОТА: СПИСКИ В PYTHON")
    print("LISTA PYTHONIS - LABORATORIUM OPUS")
    print("="*50)
    print("Основные задачи:")
    print("1. Список четных чисел")
    print("2. Срез списка")
    print("3. Случайные числа")
    print("4. Очистка списка")
    print("5. Удаление символов")
    print("6. Удаление элементов")
    print("7. Наибольший элемент")
    print("8. Наименьший элемент")
    print("9. Сумма элементов")
    print("10. Среднее арифметическое")
    print("\nСложные задачи:")
    print("11. Локальный максимум")
    print("12. Одинаковые элементы")
    print("13. Второй максимум")
    print("14. Различные элементы")
    print("0. Выход")
    print("="*50)

def main():
    """Основная функция программы"""
    tasks = {
        1: task1_even_numbers, 2: task2_slice, 3: task3_random_numbers,
        4: task4_clear_list, 5: task5_remove_vowels, 6: task6_remove_elements,
        7: task7_max_element, 8: task8_min_element, 9: task9_sum_elements,
        10: task10_average, 11: hard_task1_local_maximum, 12: hard_task2_max_duplicates,
        13: hard_task3_second_max, 14: hard_task4_unique_count
    }
    
    while True:
        show_menu()
        try:
            choice = int(input("Введите номер задачи (0 для выхода): "))
            if choice == 0:
                print("Спасибо за использование программы!")
                break
            elif choice in tasks:
                tasks[choice]()
                input("\nНажмите Enter для продолжения...")
            else:
                print("Неверный выбор! Выберите номер от 0 до 14.")
        except ValueError:
            print("Ошибка! Введите число от 0 до 14.")
        except KeyboardInterrupt:
            print("\n\nПрограмма завершена пользователем.")
            break

if __name__ == "__main__":
    main()
